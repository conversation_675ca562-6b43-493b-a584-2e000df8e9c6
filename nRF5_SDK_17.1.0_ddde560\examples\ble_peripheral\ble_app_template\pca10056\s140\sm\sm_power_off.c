#include "sm_power_off.h"
#include "nrf_drv_gpiote.h"
#include "nrf_pwr_mgmt.h"
#include "gui.h"
#include "ST7789.h"
#include "key.h"

#define POWER_OFF_TIME	3000

volatile static uint32_t sm_off_tick_ms;		//关机计时器

void sm_power_off_init(void)
{
    sm_off_tick_ms = 0;
    switch_to_next_screen(UI_SCREEN_LOG);
}


void sm_power_off_tick(void)
{
    sm_off_tick_ms++;
    if(sm_off_tick_ms >= POWER_OFF_TIME) {
        sm_off_tick_ms = 0;
        nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
        close_LCD(); // 关屏
        nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
        nrf_gpio_cfg_sense_input(LGS4056_CHRG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
        sd_power_system_off();
    }
}

void sm_power_off_event(ui_evt_t ui_evt)
{

}