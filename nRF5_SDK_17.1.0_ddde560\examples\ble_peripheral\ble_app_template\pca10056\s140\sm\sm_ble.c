#include "sm_ble.h"

static bool ble_con;
static uint32_t ble_con_ms;

void sm_ble_init(void)
{
    ble_con_ms = 0;
    if(ble_con == 0) {
        switch_to_next_screen(UI_SCREEN_BLUEDIS);
    } else switch_to_next_screen(UI_SCREEN_BLUETOOTHCONNECT);
}

void sm_ble_tick(void)
{
    ble_con_ms++;
    if(ble_con_ms >= 1000) {
        ble_con_ms = 0;
        sm_jump(SM_READY,0);
    }
}

void sm_ble_event(ui_evt_t ui_evt)
{

}

bool get_ble_con(void)
{
    return ble_con;
}

void set_ble_con(void)
{
    ble_con = 1;
}

void reset_ble_con(void)
{
    ble_con = 0;
}