#ifndef __GUI_H__
#define __GUI_H__


#include "main.h"
#include <stdint.h>
#include <stdbool.h>
#include "sm.h"
#include "app_timer.h"
#include "nrf_gpio.h"
#include "gui_guider.h"

// ��Ļ�������壨��Ҫ��ui_screen_tö�ٱ���һ�£�
#define UI_SCREEN_COUNT 11

#define LINE_WIDTH 30     // ��������
#define LINE_HEIGHT 6     // ���ֵ���
#define LINE_SPACING 15   // ���Ӽ��
#define LINE_COUNT 3      // ֻҪ������

// ƽ��ģʽ������λ�õ�ϸ�»��߲���
#define AVERAGE_DATE_LINE_WIDTH 25    // ����λ��ϸ�»��߿��ȣ�Ϊ�·��»��ߵ�2/3��
#define AVERAGE_DATE_LINE_HEIGHT 2    // ����λ��ϸ�»��߸߶ȣ���ϸ��
#define AVERAGE_DATE_LINE_SPACING 8   // �����»��߼�϶
#define AVERAGE_DATE_GROUP_SPACING 15 // ����»��߼�϶
#define AVERAGE_DATE_LINE_COUNT 6     // 6��ϸ�»���


#define GRADIENT_WIDTH 270
#define GRADIENT_HEIGHT 3
#define CANVAS_HEIGHT 32  // Ԥ��Բ����ƿռ�
#define INDICATOR_RADIUS 8 //Բ�Ĵ�С

lv_ui get_ui(void);
void gui_init(void);
void switch_to_next_screen(ui_screen_t current_screen);
void draw_gradient_line_circle(lv_obj_t *parent, uint8_t percent);
void create_empty_result_lines(lv_obj_t *parent);
void clear_result_lines(void);

// ƽ��ģʽ�µĽ���Ԫ�غ���
void create_average_date_lines(lv_obj_t *parent);
void clear_average_date_lines(void);
void clear_average_index_date_line(int index);
void create_average_bottom_lines(lv_obj_t *parent, int line_count);
void clear_average_bottom_lines(void);

void draw_gradient_line_circle_anim(lv_obj_t *parent, float bilirubin);

void show_bilirubin(float bilirubin);
void change_unit(sm_measure_unit_t unit);
void show_average_measurement_result(int measurement_index, float value);
void update_average_display(void);
void clear_average_measurement_labels(void);
void init_average_measurement_labels(lv_obj_t *parent);
void reset_average_measurement_labels(void);
bool is_average_measurement_labels_initialized(void);
void force_reinit_average_measurement_labels(lv_obj_t *parent);

void battery_create(lv_obj_t *parent);          // �������ͼ��
void battery_set_level(uint8_t percent);        // ���õ�ص��� (0-100%)
void number_square_create(lv_obj_t *parent, int number);
void number_square_set_number(int number);
void bluetooth_image_create(lv_obj_t *parent);
void bluetooth_image_set_visible(bool visible);
#endif
